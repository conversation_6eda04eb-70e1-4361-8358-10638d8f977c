#!/usr/bin/env python3
"""Main script to demonstrate browser automation with Bright Data API."""

import asyncio
from loguru import logger
from src.browser_automation import Voya<PERSON>r<PERSON><PERSON><PERSON>, visit_mql5


async def demo_basic_visit():
    """Demonstrate basic page visit functionality."""
    logger.info("=== Demo: Basic Page Visit ===")
    
    try:
        # Simple visit using convenience function
        content = await visit_mql5()
        logger.success(f"Retrieved content from MQL5.com ({len(content)} characters)")
        
        # Show first 500 characters of content
        preview = content[:500] + "..." if len(content) > 500 else content
        logger.info(f"Content preview:\n{preview}")
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")


async def demo_advanced_features():
    """Demonstrate advanced browser features."""
    logger.info("=== Demo: Advanced Features ===")
    
    try:
        async with VoyagrBrowser() as browser:
            # Visit MQL5.com
            await browser.visit_url("https://www.mql5.com")
            
            # Get page information
            page_info = await browser.get_page_info()
            logger.info(f"Page info: {page_info}")
            
            # Take a screenshot
            screenshot_path = await browser.take_screenshot("mql5_screenshot.png")
            logger.success(f"Screenshot saved: {screenshot_path}")
            
            # Wait a bit to see the page
            logger.info("Waiting 5 seconds...")
            await asyncio.sleep(5)
            
    except Exception as e:
        logger.error(f"Advanced demo failed: {e}")


async def main():
    """Main function to run demonstrations."""
    logger.info("Starting Voyagr Browser Automation Demo")
    
    try:
        # Run basic demo
        await demo_basic_visit()
        
        print("\n" + "="*50 + "\n")
        
        # Run advanced demo
        await demo_advanced_features()
        
    except KeyboardInterrupt:
        logger.info("Demo interrupted by user")
    except Exception as e:
        logger.error(f"Demo failed: {e}")
    finally:
        logger.info("Demo completed")


if __name__ == "__main__":
    # Configure logging
    logger.add("logs/browser_automation.log", rotation="1 MB", level="INFO")
    
    # Run the demo
    asyncio.run(main())
