# Voyagr Browser Automation

A Python-based browser automation system using Bright Data's Browser API for web scraping and automation tasks.

## Features

- **Bright Data Integration**: Uses Bright Data's Browser API with residential proxies and unblocking capabilities
- **Playwright Support**: Built on Playwright for robust browser automation
- **CAPTCHA Handling**: Automatic CAPTCHA detection and solving
- **Extensible Design**: Easy to extend for login functionality and complex automation tasks
- **Async/Await**: Modern Python async programming for better performance

## Setup

### 1. Install Dependencies

```bash
# Install Poetry if you haven't already
curl -sSL https://install.python-poetry.org | python3 -

# Install project dependencies
poetry install
```

### 2. Install Playwright Browsers

```bash
poetry run playwright install chromium
```

### 3. Configuration

The project is already configured with your Bright Data credentials in `.env`:

```
BRIGHT_DATA_AUTH=brd-customer-hl_1388c9f0-zone-scraping_browser_test:xhwha0fp8q47
TARGET_URL=https://www.mql5.com
BROWSER_TIMEOUT=120000
DETECT_TIMEOUT=10000
```

## Usage

### Basic Usage

Run the demo script to test the connection:

```bash
poetry run python main.py
```

### Programmatic Usage

```python
import asyncio
from src.browser_automation import VoyagrBrowser

async def example():
    async with VoyagrBrowser() as browser:
        # Visit MQL5.com
        content = await browser.visit_url("https://www.mql5.com")
        
        # Get page info
        info = await browser.get_page_info()
        print(f"Page title: {info['title']}")
        
        # Take screenshot
        await browser.take_screenshot("page.png")

# Run the example
asyncio.run(example())
```

### Simple Page Visit

```python
from src.browser_automation import visit_mql5

# Simple convenience function
content = await visit_mql5()
```

## Project Structure

```
voyagr_browser/
├── src/
│   ├── __init__.py
│   ├── config.py              # Configuration management
│   └── browser_automation.py  # Main browser automation class
├── main.py                    # Demo script
├── pyproject.toml            # Poetry dependencies
├── .env                      # Environment variables
└── README.md
```

## Available Methods

### VoyagrBrowser Class

- `connect()` - Connect to Bright Data browser
- `create_page()` - Create a new browser page
- `visit_url(url)` - Navigate to a URL
- `wait_for_captcha()` - Handle CAPTCHA detection/solving
- `get_page_info()` - Get current page information
- `take_screenshot(path)` - Take a screenshot
- `close()` - Close browser connection

## Next Steps

This foundation is ready for extending with:

1. **Login Functionality**: Add methods for form filling and authentication
2. **Data Extraction**: Add methods for scraping specific elements
3. **Action Automation**: Add methods for clicking, scrolling, form submission
4. **Session Management**: Add persistent session handling
5. **Error Handling**: Enhanced error recovery and retry logic

## Bright Data Configuration

- **Host**: brd.superproxy.io
- **Port**: 9222 (Default), 9515 (Selenium)
- **Zone**: scraping_browser_test
- **Features**: Residential proxies, CAPTCHA solving, fingerprint management
