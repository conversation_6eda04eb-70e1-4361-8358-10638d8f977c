[tool.poetry]
name = "voyagr-browser"
version = "0.1.0"
description = "Browser automation system using Bright Data API for web scraping and automation"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.8"
playwright = "^1.40.0"
python-dotenv = "^1.0.0"
pydantic = "^2.5.0"
loguru = "^0.7.2"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
black = "^23.0.0"
isort = "^5.12.0"
flake8 = "^6.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
