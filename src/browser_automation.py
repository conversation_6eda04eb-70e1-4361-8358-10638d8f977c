"""Browser automation using Bright Data API with <PERSON><PERSON>."""

import asyncio
from typing import Optional, Dict, Any, List
from playwright.async_api import <PERSON><PERSON>, async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, CDPSession
from loguru import logger
from .config import config


class VoyagrBrowser:
    """Browser automation class using Bright Data API."""
    
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        self.cdp_session: Optional[CDPSession] = None
        self.playwright: Optional[Playwright] = None
        
    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
        
    async def connect(self) -> None:
        """Connect to Bright Data browser."""
        try:
            logger.info("Connecting to Bright Data Browser...")
            
            # Validate authentication
            if config.bright_data_auth == "SBR_ZONE_FULL_USERNAME:SBR_ZONE_PASSWORD":
                raise ValueError(
                    "Please provide valid Bright Data credentials in the .env file"
                )
            
            # Initialize Playwright
            self.playwright = await async_playwright().start()
            
            # Connect to Bright Data browser via CDP
            self.browser = await self.playwright.chromium.connect_over_cdp(
                config.endpoint_url
            )
            
            logger.success("Successfully connected to Bright Data Browser")
            
        except Exception as e:
            logger.error(f"Failed to connect to browser: {e}")
            raise
            
    async def create_page(self) -> Page:
        """Create a new page and CDP session."""
        if not self.browser:
            raise RuntimeError("Browser not connected. Call connect() first.")
            
        try:
            logger.info("Creating new page...")
            self.page = await self.browser.new_page()
            
            # Create CDP session for advanced features
            self.cdp_session = await self.page.context.new_cdp_session(self.page)
            
            logger.success("Page created successfully")
            return self.page
            
        except Exception as e:
            logger.error(f"Failed to create page: {e}")
            raise
            
    async def visit_url(self, url: str = None) -> str:
        """Visit a URL and return the page content."""
        target_url = url or config.target_url
        
        try:
            if not self.page:
                await self.create_page()
                
            logger.info(f"Navigating to {target_url}...")
            
            # Navigate to the URL with timeout
            await self.page.goto(target_url, timeout=config.browser_timeout)
            
            logger.success(f"Successfully navigated to {target_url}")
            
            # Get page content
            content = await self.page.content()
            logger.info(f"Retrieved page content ({len(content)} characters)")
            
            return content
            
        except Exception as e:
            logger.error(f"Failed to visit URL {target_url}: {e}")
            raise
            
    async def wait_for_captcha(self) -> str:
        """Wait for CAPTCHA detection and solving."""
        if not self.cdp_session:
            raise RuntimeError("CDP session not available. Create a page first.")
            
        try:
            logger.info("Waiting for CAPTCHA detection and solving...")
            
            result = await self.cdp_session.send('Captcha.waitForSolve', {
                'detectTimeout': config.detect_timeout,
            })
            
            status = result.get('status', 'unknown')
            logger.info(f"CAPTCHA status: {status}")
            
            return status
            
        except Exception as e:
            logger.error(f"CAPTCHA handling failed: {e}")
            raise
            
    async def get_page_info(self) -> Dict[str, Any]:
        """Get current page information."""
        if not self.page:
            raise RuntimeError("No page available. Visit a URL first.")
            
        try:
            info = {
                'url': self.page.url,
                'title': await self.page.title(),
                'viewport': self.page.viewport_size,
            }
            
            logger.info(f"Page info: {info}")
            return info
            
        except Exception as e:
            logger.error(f"Failed to get page info: {e}")
            raise
            
    async def take_screenshot(self, path: str = "screenshot.png") -> str:
        """Take a screenshot of the current page."""
        if not self.page:
            raise RuntimeError("No page available. Visit a URL first.")
            
        try:
            logger.info(f"Taking screenshot: {path}")
            await self.page.screenshot(path=path)
            logger.success(f"Screenshot saved to {path}")
            return path
            
        except Exception as e:
            logger.error(f"Failed to take screenshot: {e}")
            raise
            
    async def close(self) -> None:
        """Close the browser connection."""
        try:
            if self.browser:
                logger.info("Closing browser connection...")
                await self.browser.close()
                self.browser = None
                self.page = None
                self.cdp_session = None
                
            if self.playwright:
                await self.playwright.stop()
                self.playwright = None
                
            logger.success("Browser connection closed")
            
        except Exception as e:
            logger.error(f"Error closing browser: {e}")


# Convenience function for simple page visits
async def visit_mql5() -> str:
    """Simple function to visit MQL5.com and return content."""
    async with VoyagrBrowser() as browser:
        content = await browser.visit_url("https://www.mql5.com")
        return content
