"""Configuration management for Voyagr Browser Automation."""

import os
from typing import Optional
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class BrowserConfig(BaseSettings):
    """Configuration settings for browser automation."""
    
    # Bright Data credentials
    bright_data_auth: str = Field(
        default="brd-customer-hl_1388c9f0-zone-scraping_browser_test:xhwha0fp8q47",
        env="BRIGHT_DATA_AUTH",
        description="Bright Data authentication in format username:password"
    )
    
    # Browser settings
    browser_timeout: int = Field(
        default=120000,
        env="BROWSER_TIMEOUT", 
        description="Browser timeout in milliseconds"
    )
    
    detect_timeout: int = Field(
        default=10000,
        env="DETECT_TIMEOUT",
        description="CAPTCHA detection timeout in milliseconds"
    )
    
    # Default target URL
    target_url: str = Field(
        default="https://www.mql5.com",
        env="TARGET_URL",
        description="Default target URL to visit"
    )
    
    # Bright Data endpoint
    endpoint_host: str = Field(
        default="brd.superproxy.io",
        description="Bright Data endpoint host"
    )
    
    endpoint_port: int = Field(
        default=9222,
        description="Bright Data endpoint port"
    )
    
    @property
    def endpoint_url(self) -> str:
        """Get the WebSocket endpoint URL for Bright Data."""
        return f"wss://{self.bright_data_auth}@{self.endpoint_host}:{self.endpoint_port}"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# Global config instance
config = BrowserConfig()
